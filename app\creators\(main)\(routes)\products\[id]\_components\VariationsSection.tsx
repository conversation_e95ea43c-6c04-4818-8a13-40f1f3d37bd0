"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Edit3, Trash, Plus, X, Package } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories";
import { useUpdateProductVariations } from "@/lib/hooks/use-products";
import {
  SIZE_OPTIONS,
  COLOR_OPTIONS,
  ProductVariation,
  BaleVariation,
  Product,
  Bale,
  ProductOrBale
} from "@/lib/types/products";

// Simple reusable detail display
const DetailItem = ({ label, value }: { label: string; value: string | number }) => (
  <div className="text-sm">
    <p className="text-gray-500">{label}</p>
    <p className="font-medium text-gray-800">{value || "Not specified"}</p>
  </div>
);

// Form validation schema - exact copy from VariationsForm
const variationsSchema = z.object({
  variations: z.array(z.object({
    color: z.string().min(1, "Color is required"),
    size: z.string().min(1, "Size is required"),
    quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
    price: z.union([z.string(), z.number()]).pipe(z.coerce.number().min(0.01, "Price must be greater than 0")),
    salePrice: z.union([z.string(), z.number()]).transform((val) => {
      if (val === "" || val === null || val === undefined) return undefined;
      return Number(val);
    }).optional(),
    saleStartDate: z.string().optional(),
    saleEndDate: z.string().optional(),
  }).refine((data) => {
    // If sale price is set, both start and end dates are required
    if (data.salePrice && data.salePrice > 0) {
      if (!data.saleStartDate || !data.saleEndDate) {
        return false;
      }

      // Validate that sale price is less than regular price
      if (data.salePrice >= data.price) {
        return false;
      }

      // Validate that start date is not in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const startDate = new Date(data.saleStartDate);
      // if (startDate < today) {
      //   return false;
      // }

      // Validate that end date is after start date
      const endDate = new Date(data.saleEndDate);
      if (endDate <= startDate) {
        return false;
      }
    }
    return true;
  }, {
    message: "Sale pricing requires valid start/end dates, sale price must be less than regular price, and start date cannot be in the past"
  })).min(1, "At least one variation is required"),
  relatedCategories: z.array(z.string()).optional(),
});

type VariationsFormData = z.infer<typeof variationsSchema>;

interface VariationsSectionProps {
  product: ProductOrBale;
}

const VariationsSection: React.FC<VariationsSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(false);
  const isProduct = product.type === 'product';
  const productData = product as Product;
  const baleData = product as Bale;
  const { categories } = useCategoriesHierarchy();
  const updateVariationsMutation = useUpdateProductVariations();

  // Local state
  const [selectedCategory, setSelectedCategory] = useState("");

  // Helper function to convert ISO datetime to datetime-local format
  const formatDateTimeForInput = (isoString: string) => {
    if (!isoString) return '';
    try {
      const date = new Date(isoString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Convert product variations to form format
  const getFormVariations = () => {
    if (product.variations && product.variations.length > 0) {
      return product.variations.map((variation: any) => ({
        color: variation.color || "",
        size: variation.size || "",
        quantity: variation.quantity || 1,
        price: variation.price || 0,
        salePrice: variation.salePrice || undefined,
        saleStartDate: formatDateTimeForInput(variation.saleStartDate) || "",
        saleEndDate: formatDateTimeForInput(variation.saleEndDate) || "",
      }));
    }
    return [{
      color: "",
      size: "",
      quantity: 1,
      price: 0,
      salePrice: undefined,
      saleStartDate: "",
      saleEndDate: "",
    }];
  };

  // Helper function to extract category IDs
  const getRelatedCategoryIds = () => {
    if (!product.relatedCategories) return [];

    return product.relatedCategories.map((category: any) => {
      // If it's an object with _id, extract the _id
      if (typeof category === 'object' && category._id) {
        return category._id;
      }
      // If it's already a string ID, return as is
      return category;
    });
  };

  // Form setup - exact copy from VariationsForm
  const form = useForm<VariationsFormData>({
    resolver: zodResolver(variationsSchema),
    defaultValues: {
      variations: getFormVariations(),
      relatedCategories: getRelatedCategoryIds(),
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  // Helper function to get today's date in YYYY-MM-DDTHH:MM format for datetime-local
  const getTodayDateTime = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const hours = String(today.getHours()).padStart(2, '0');
    const minutes = String(today.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const toggleEdit = () => setIsEditing(!isEditing);

  // Add new variation
  const addVariation = () => {
    const currentVariations = watch('variations') || [];
    setValue('variations', [
      ...currentVariations,
      {
        color: "",
        size: "",
        quantity: 1,
        price: 0,
        salePrice: undefined,
        saleStartDate: "",
        saleEndDate: "",
      }
    ]);
  };

  // Remove variation
  const removeVariation = (index: number) => {
    const currentVariations = watch('variations') || [];
    if (currentVariations.length > 1) {
      setValue('variations', currentVariations.filter((_, i) => i !== index));
    }
  };

  // Handle related categories
  const addRelatedCategory = () => {
    if (selectedCategory) {
      const currentCategories = watch('relatedCategories') || [];
      if (!currentCategories.includes(selectedCategory)) {
        setValue('relatedCategories', [...currentCategories, selectedCategory]);
      }
      setSelectedCategory("");
    }
  };

  const removeRelatedCategory = (index: number) => {
    const currentCategories = watch('relatedCategories') || [];
    setValue('relatedCategories', currentCategories.filter((_, i) => i !== index));
  };

  // Helper function to prepare variations data for API
  const prepareVariationsData = (formData: VariationsFormData) => {
    const existingVariations = product.variations || [];

    return formData.variations.map((formVariation, index) => {
      const existingVariation = existingVariations[index];

      // Prepare the variation object
      const variationData: any = {
        color: formVariation.color,
        size: formVariation.size,
        quantity: formVariation.quantity,
        price: formVariation.price,
      };

      // Add _id if this is an existing variation
      if (existingVariation && existingVariation._id) {
        variationData._id = existingVariation._id;
      }

      // Add sale information if provided
      if (formVariation.salePrice && formVariation.salePrice > 0) {
        variationData.salePrice = formVariation.salePrice;

        // Convert datetime-local format to ISO format for API
        if (formVariation.saleStartDate) {
          variationData.saleStartDate = new Date(formVariation.saleStartDate).toISOString();
        }
        if (formVariation.saleEndDate) {
          variationData.saleEndDate = new Date(formVariation.saleEndDate).toISOString();
        }
      }

      return variationData;
    });
  };

  const onSubmit = async (data: VariationsFormData) => {
    try {
      console.log('Saving variations:', data);

      const variationsData = prepareVariationsData(data);

      await updateVariationsMutation.mutateAsync({
        productId: product._id,
        variations: variationsData,
        relatedCategories: data.relatedCategories
      });

      setIsEditing(false);
    } catch (error) {
      console.error('Variations update failed:', error);
      // Error is handled by the mutation
    }
  };

  if (isEditing) {
    return (
      <div className="w-full">
        <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-bold">Product Variations</h2>
            <Button variant="ghost" onClick={toggleEdit}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Variations */}
          <div className="space-y-6">
            {watch('variations')?.map((variation, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium">Variation {index + 1}</h3>
                  {watch('variations')?.length > 1 && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeVariation(index)}
                    >
                      <Trash className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Color <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.color`}
                      control={control}
                      render={({ field }) => (
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger className={errors.variations?.[index]?.color ? 'border-red-300' : ''}>
                            <SelectValue placeholder="Select color" />
                          </SelectTrigger>
                          <SelectContent>
                            {COLOR_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.variations?.[index]?.color && (
                      <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.color?.message}</p>
                    )}
                  </div>

                  {/* Size */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Size <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.size`}
                      control={control}
                      render={({ field }) => (
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger className={errors.variations?.[index]?.size ? 'border-red-300' : ''}>
                            <SelectValue placeholder="Select size" />
                          </SelectTrigger>
                          <SelectContent>
                            {SIZE_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.variations?.[index]?.size && (
                      <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.size?.message}</p>
                    )}
                  </div>

                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Quantity <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.quantity`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min="1"
                          className={errors.variations?.[index]?.quantity ? 'border-red-300' : ''}
                        />
                      )}
                    />
                    {errors.variations?.[index]?.quantity && (
                      <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.quantity?.message}</p>
                    )}
                  </div>

                  {/* Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Price (GHS) <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name={`variations.${index}.price`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          step="0.01"
                          min="0"
                          className={errors.variations?.[index]?.price ? 'border-red-300' : ''}
                        />
                      )}
                    />
                    {errors.variations?.[index]?.price && (
                      <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.price?.message}</p>
                    )}
                  </div>

                  {/* Sale Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Sale Price (GHS)
                      <span className="text-xs text-gray-500 block">Optional - requires start & end dates</span>
                    </label>
                    <Controller
                      name={`variations.${index}.salePrice`}
                      control={control}
                      render={({ field }) => {
                        const regularPrice = watch(`variations.${index}.price`);
                        const salePrice = field.value;
                        const hasError = salePrice && regularPrice && salePrice >= regularPrice;

                        return (
                          <div>
                            <Input
                              {...field}
                              type="number"
                              step="0.01"
                              min="0"
                              max={regularPrice ? (regularPrice - 0.01).toString() : undefined}
                              placeholder="Optional"
                              className={hasError ? 'border-red-300' : ''}
                              onChange={(e) => {
                                field.onChange(e);
                                // Clear dates if sale price is removed
                                if (!e.target.value) {
                                  setValue(`variations.${index}.saleStartDate`, '');
                                  setValue(`variations.${index}.saleEndDate`, '');
                                }
                              }}
                            />
                            {hasError && (
                              <p className="text-xs text-red-600 mt-1">
                                Sale price must be less than regular price (GHS {regularPrice})
                              </p>
                            )}
                          </div>
                        );
                      }}
                    />
                  </div>

                  {/* Sale Start Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Sale Start Date
                      {watch(`variations.${index}.salePrice`) && (
                        <span className="text-red-500"> *</span>
                      )}
                    </label>
                    <Controller
                      name={`variations.${index}.saleStartDate`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="datetime-local"
                          // min={getTodayDateTime()}
                          disabled={!watch(`variations.${index}.salePrice`)}
                          className={
                            watch(`variations.${index}.salePrice`) && !field.value
                              ? 'border-red-300'
                              : ''
                          }
                        />
                      )}
                    />
                    {watch(`variations.${index}.salePrice`) && !watch(`variations.${index}.saleStartDate`) && (
                      <p className="text-xs text-red-600 mt-1">Start date is required for sale pricing</p>
                    )}
                  </div>

                  {/* Sale End Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      Sale End Date
                      {watch(`variations.${index}.salePrice`) && (
                        <span className="text-red-500"> *</span>
                      )}
                    </label>
                    <Controller
                      name={`variations.${index}.saleEndDate`}
                      control={control}
                      render={({ field }) => {
                        const startDate = watch(`variations.${index}.saleStartDate`);
                        return (
                          <Input
                            {...field}
                            type="datetime-local"
                            min={startDate || getTodayDateTime()}
                            disabled={!watch(`variations.${index}.salePrice`)}
                            className={
                              watch(`variations.${index}.salePrice`) && !field.value
                                ? 'border-red-300'
                                : ''
                            }
                          />
                        );
                      }}
                    />
                    {watch(`variations.${index}.salePrice`) && !watch(`variations.${index}.saleEndDate`) && (
                      <p className="text-xs text-red-600 mt-1">End date is required for sale pricing</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add Variation Button */}
          <Button
            type="button"
            onClick={addVariation}
            className="mt-4 w-full"
            variant="outline"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Variation
          </Button>

          {/* Related Categories */}
          <div className="mt-8">
            <label className="block text-sm font-medium text-gray-900 mb-2">Related Categories</label>
            <div className="flex gap-2 mb-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select related category" />
                </SelectTrigger>
                <SelectContent>
                  {categories?.map((category) => (
                    <SelectItem key={category._id} value={category._id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={addRelatedCategory}
                disabled={!selectedCategory}
                size="sm"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {watch('relatedCategories')?.map((categoryId, index) => {
                // Ensure categoryId is a string
                const categoryIdString = typeof categoryId === 'object' ?
                  (categoryId as any)?._id || (categoryId as any)?.id || String(categoryId) :
                  String(categoryId);
                const category = categories?.find(cat => cat._id === categoryIdString);
                return (
                  <Badge key={index} variant="outline" className="flex items-center gap-1">
                    {category?.name || categoryIdString}
                    <button
                      type="button"
                      onClick={() => removeRelatedCategory(index)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                );
              })}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 mt-8">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditing(false)}
              disabled={updateVariationsMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="px-8"
              disabled={updateVariationsMutation.isPending}
            >
              {updateVariationsMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    );
  }

  // View Mode
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          {isProduct ? 'Product Variations' : 'Bale Variations'}
        </h3>
        <Button variant="ghost" onClick={toggleEdit}>
          <Edit3 className="w-5 h-5" />
        </Button>
      </div>
      <div className="space-y-4">
        {product.variations && product.variations.length > 0 ? (
          product.variations.map((variation: any, index: number) => (
            <div key={variation._id || index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-gray-800">
                  Variation {index + 1}
                </h4>
                <div className="flex items-center gap-2">
                  {isProduct ? (
                    <>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                        {variation.color}
                      </span>
                      <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">
                        {variation.size}
                      </span>
                    </>
                  ) : (
                    <span className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full">
                      {variation.identifier}
                    </span>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <DetailItem label="Quantity" value={variation.quantity} />
                <DetailItem label="Price" value={`GHS ${variation.price}`} />
                {isProduct && variation.salePrice && (
                  <DetailItem label="Sale Price" value={`GHS ${variation.salePrice}`} />
                )}
                {isProduct && variation.currentPrice && variation.currentPrice !== variation.price && (
                  <DetailItem label="Current Price" value={`GHS ${variation.currentPrice}`} />
                )}
              </div>

              {(variation.saleStartDate || variation.saleEndDate) && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {variation.saleStartDate && (
                      <DetailItem
                        label="Sale Start"
                        value={new Date(variation.saleStartDate).toLocaleDateString()}
                      />
                    )}
                    {variation.saleEndDate && (
                      <DetailItem
                        label="Sale End"
                        value={new Date(variation.saleEndDate).toLocaleDateString()}
                      />
                    )}
                  </div>
                </div>
              )}

              {/* Stock Status Indicator */}
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">Stock Status:</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    variation.quantity === 0 ? 'bg-red-100 text-red-700' :
                    variation.quantity <= 5 ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {variation.quantity === 0 ? 'Out of Stock' :
                     variation.quantity <= 5 ? 'Low Stock' : 'In Stock'}
                  </span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Package className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p>No variations available</p>
            <p className="text-sm">Click edit to add product variations</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VariationsSection;
