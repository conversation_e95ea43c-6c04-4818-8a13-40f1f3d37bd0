"use client"

import * as React from "react"
import { useF<PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useProductForm } from "@/lib/contexts/ProductFormContext"
import { BaleSpecificationsFormData } from "@/lib/types/products"

// Form validation schema for bale specifications
const baleSpecificationsSchema = z.object({
  material: z.string().optional(),
  origin: z.string().optional(),
  packagingType: z.string().optional(),
  gradeQuality: z.string().optional(),
  sortingMethod: z.string().optional(),
  storageConditions: z.string().optional(),
  certifications: z.string().optional(),
  notes: z.string().optional(),
})

type BaleSpecsFormData = z.infer<typeof baleSpecificationsSchema>

interface BaleSpecificationsFormProps {
  onNext: () => void;
  onBack: () => void;
}

// Common options for bale specifications
const MATERIAL_OPTIONS = [
  { value: "cotton", label: "Cotton" },
  { value: "polyester", label: "Polyester" },
  { value: "mixed", label: "Mixed Materials" },
  { value: "denim", label: "Denim" },
  { value: "wool", label: "Wool" },
  { value: "silk", label: "Silk" },
  { value: "linen", label: "Linen" },
  { value: "synthetic", label: "Synthetic" },
]

const PACKAGING_OPTIONS = [
  { value: "compressed", label: "Compressed Bale" },
  { value: "loose", label: "Loose Packed" },
  { value: "vacuum", label: "Vacuum Sealed" },
  { value: "wrapped", label: "Plastic Wrapped" },
  { value: "boxed", label: "Boxed" },
]

const GRADE_OPTIONS = [
  { value: "premium", label: "Premium Grade" },
  { value: "standard", label: "Standard Grade" },
  { value: "economy", label: "Economy Grade" },
  { value: "mixed", label: "Mixed Grade" },
]

const SORTING_OPTIONS = [
  { value: "unsorted", label: "Unsorted" },
  { value: "by-type", label: "Sorted by Type" },
  { value: "by-size", label: "Sorted by Size" },
  { value: "by-brand", label: "Sorted by Brand" },
  { value: "by-season", label: "Sorted by Season" },
]

export default function BaleSpecificationsForm({ onNext, onBack }: BaleSpecificationsFormProps) {
  const { specifications, updateSpecifications } = useProductForm()
  
  // Cast specifications to BaleSpecificationsFormData for type safety
  const baleSpecs = specifications as BaleSpecificationsFormData
  
  // Form setup
  const form = useForm<BaleSpecsFormData>({
    resolver: zodResolver(baleSpecificationsSchema),
    defaultValues: {
      material: baleSpecs.material || "",
      origin: baleSpecs.origin || "",
      packagingType: baleSpecs.packagingType || "",
      gradeQuality: (baleSpecs as any).gradeQuality || "",
      sortingMethod: (baleSpecs as any).sortingMethod || "",
      storageConditions: (baleSpecs as any).storageConditions || "",
      certifications: (baleSpecs as any).certifications || "",
      notes: (baleSpecs as any).notes || "",
    },
  })

  const { control, handleSubmit, formState: { errors } } = form

  const onSubmit = (data: BaleSpecsFormData) => {
    updateSpecifications(data)
    onNext()
  }

  return (
    <div className="w-full">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Bale Specifications
        </h1>
        <p className="text-gray-600">
          Provide additional details about your bale's material composition, packaging, and quality.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="w-full p-4 rounded-lg bg-white mb-4">
        {/* Material and Origin */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Primary Material
            </label>
            <Controller
              name="material"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select primary material" />
                  </SelectTrigger>
                  <SelectContent>
                    {MATERIAL_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Origin Details
            </label>
            <Controller
              name="origin"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., UK charity shops, US retail returns"
                />
              )}
            />
          </div>
        </div>

        {/* Packaging and Grade */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Packaging Type
            </label>
            <Controller
              name="packagingType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select packaging type" />
                  </SelectTrigger>
                  <SelectContent>
                    {PACKAGING_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Grade Quality
            </label>
            <Controller
              name="gradeQuality"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select grade quality" />
                  </SelectTrigger>
                  <SelectContent>
                    {GRADE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>

        {/* Sorting and Storage */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Sorting Method
            </label>
            <Controller
              name="sortingMethod"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sorting method" />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTING_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Storage Conditions
            </label>
            <Controller
              name="storageConditions"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., Dry warehouse, climate controlled"
                />
              )}
            />
          </div>
        </div>

        {/* Certifications */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Certifications
          </label>
          <Controller
            name="certifications"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="e.g., OEKO-TEX, GOTS, Recycled content certified"
              />
            )}
          />
        </div>

        {/* Additional Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Additional Notes
          </label>
          <Controller
            name="notes"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Any additional information about the bale composition, special characteristics, or handling instructions..."
                rows={3}
              />
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-between pt-6 border-t">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit">
            Continue to Variations
          </Button>
        </div>
      </form>

      {/* Information Card */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Specification Guidelines</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Material composition helps buyers understand the bale contents</li>
          <li>• Origin details provide transparency about sourcing</li>
          <li>• Packaging type affects shipping and storage requirements</li>
          <li>• Grade quality indicates the overall condition and value</li>
          <li>• Sorting method helps buyers understand organization level</li>
        </ul>
      </div>
    </div>
  )
}
